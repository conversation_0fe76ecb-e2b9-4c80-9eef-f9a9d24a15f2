package com.goodlab.controller;



import com.goodlab.pojo.Article;
import com.goodlab.pojo.Result;
import com.goodlab.service.ArticleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/article")
public class ArticleController {


    @Autowired
    private ArticleService articleService;

    // 新增文章
    @PostMapping
    public Result  add(@RequestBody @Validated Article article){

        articleService.add(article);

        return Result.success();
    }


}
